<template>
  <div class="note-form-container">
    <van-nav-bar
      :title="isEdit ? '编辑笔记' : '新建笔记'"
      left-text="返回"
      left-arrow
      @click-left="router.back()"
    />
    
    <div class="form-content">
      <!-- 笔记预览区域 -->
      <div v-if="notePreview.title || notePreview.mainImage" class="note-preview">
        <van-cell-group inset>
          <van-cell title="笔记预览" />
          <div class="preview-content">
            <div v-if="notePreview.mainImage" class="main-image">
              <van-image
                :src="notePreview.mainImage"
                width="100%"
                height="200"
                fit="cover"
                radius="8"
              />
            </div>
            <div v-if="notePreview.title" class="preview-title">
              {{ notePreview.title }}
            </div>
          </div>
        </van-cell-group>
      </div>

      <van-form @submit="handleSubmit">
        <van-cell-group inset>
          <van-field
            v-model="form.title"
            name="title"
            label="笔记标题"
            placeholder="请输入笔记标题"
            :rules="[{ required: true, message: '请输入笔记标题' }]"
          >
            <template #button>
              <van-button size="small" @click="copyTitle">复制</van-button>
            </template>
          </van-field>
          <van-field
            v-model="form.body"
            name="body"
            label="笔记正文"
            type="textarea"
            placeholder="请输入笔记正文内容"
            rows="6"
          >
            <template #button>
              <van-button size="small" @click="copyBody">复制</van-button>
            </template>
          </van-field>
          <van-field
            v-model="form.tags"
            name="tags"
            label="标签"
            placeholder="请输入标签，用逗号分隔"
          >
            <template #button>
              <van-button size="small" @click="copyTags">复制</van-button>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 文件上传区域 -->
        <van-cell-group inset>
          <FileUpload
            v-model="form.cover_images_candidate"
            title="封面候选图片"
            :max-count="5"
            accept="image/*"
            upload-text="上传封面"
            type="image"
          />

          <FileUpload
            v-model="form.note_images"
            title="笔记图片"
            :max-count="30"
            accept="image/*"
            upload-text="上传图片"
            type="image"
          />

          <FileUpload
            v-model="form.videos"
            title="笔记视频"
            :max-count="5"
            accept="video/*"
            upload-text="上传视频"
            type="video"
          />
        </van-cell-group>
        
        <div class="submit-button">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
          >
            {{ isEdit ? '更新笔记' : '创建笔记' }}
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import api from '@/utils/api'
import FileUpload from '@/components/FileUpload.vue'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const isEdit = computed(() => route.name === 'note-edit')

const form = reactive({
  title: '',
  body: '',
  tags: '',
  cover_images_candidate: [] as string[],
  note_images: [] as string[],
  videos: [] as string[]
})

// 笔记预览数据
const notePreview = reactive({
  title: '',
  mainImage: ''
})

const loadNote = async () => {
  if (!isEdit.value) return

  try {
    const response = await api.get(`/api/v1/notes/${route.params.id}`)
    const note = response.data

    Object.assign(form, {
      title: note.title,
      body: note.body || '',
      tags: note.tags || '',
      cover_images_candidate: note.cover_images_candidate || [],
      note_images: note.note_images || [],
      videos: note.videos || []
    })
  } catch (error) {
    console.error('Failed to load note:', error)
    showToast('加载笔记信息失败')
    router.back()
  }
}

const copyTitle = async () => {
  if (!form.title) {
    showToast('标题为空')
    return
  }
  try {
    await navigator.clipboard.writeText(form.title)
    showToast('标题已复制到剪贴板')
  } catch (error) {
    showToast('复制失败')
  }
}

const copyBody = async () => {
  if (!form.body) {
    showToast('正文为空')
    return
  }
  try {
    await navigator.clipboard.writeText(form.body)
    showToast('正文已复制到剪贴板')
  } catch (error) {
    showToast('复制失败')
  }
}

const copyTags = async () => {
  if (!form.tags) {
    showToast('标签为空')
    return
  }
  try {
    await navigator.clipboard.writeText(form.tags)
    showToast('标签已复制到剪贴板')
  } catch (error) {
    showToast('复制失败')
  }
}

const handleSubmit = async () => {
  loading.value = true

  try {
    if (isEdit.value) {
      await api.put(`/api/v1/notes/${route.params.id}`, form)
      showToast('更新成功')
    } else {
      await api.post(`/api/v1/products/${route.params.productId}/notes/`, form)
      showToast('创建成功')
    }

    router.back()
  } catch (error) {
    console.error('Failed to save note:', error)
    showToast(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

// 监听表单变化，更新预览
watch(() => form.title, (newTitle) => {
  notePreview.title = newTitle
})

watch(() => form.cover_images_candidate, (newImages) => {
  notePreview.mainImage = newImages && newImages.length > 0 ? newImages[0] : ''
}, { deep: true })

onMounted(() => {
  loadNote()
})
</script>

<style scoped>
.note-form-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.form-content {
  padding: 16px;
}

.submit-button {
  margin-top: 24px;
}

.note-preview {
  margin-bottom: 16px;
}

.preview-content {
  padding: 16px;
}

.main-image {
  margin-bottom: 12px;
}

.preview-title {
  font-size: 18px;
  font-weight: 500;
  color: #323233;
  line-height: 1.4;
}
</style>
