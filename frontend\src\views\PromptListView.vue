<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">AI提示词</h1>
      <van-button
        v-if="authStore.isAdmin"
        type="primary"
        icon="plus"
        @click="router.push('/prompts/create')"
        class="create-btn"
      >
        新建提示词
      </van-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchQuery"
        placeholder="搜索提示词标题或内容"
        @search="loadPrompts"
        @clear="loadPrompts"
        shape="round"
      />
    </div>

    <!-- 提示词列表 -->
    <div class="prompt-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <van-cell-group inset v-for="prompt in prompts" :key="prompt.id">
            <van-cell
              :title="prompt.title"
              :label="prompt.content.substring(0, 150) + (prompt.content.length > 150 ? '...' : '')"
            >
              <template #extra>
                <div class="prompt-actions">
                  <van-button
                    size="small"
                    @click="copyPrompt(prompt)"
                    icon="copy"
                  >
                    复制
                  </van-button>
                  <van-button
                    v-if="authStore.isAdmin"
                    size="small"
                    type="primary"
                    @click="editPrompt(prompt)"
                    icon="edit"
                  >
                    编辑
                  </van-button>
                  <van-button
                    v-if="authStore.isAdmin"
                    size="small"
                    type="danger"
                    @click="confirmDelete(prompt)"
                    icon="delete"
                  >
                    删除
                  </van-button>
                </div>
              </template>
            </van-cell>

            <!-- 提示词详情 -->
            <van-cell v-if="prompt.created_by">
              <div class="prompt-meta">
                <span class="create-info">创建时间: {{ formatDate(prompt.created_at) }}</span>
                <span v-if="prompt.updated_at !== prompt.created_at" class="update-info">
                  更新时间: {{ formatDate(prompt.updated_at) }}
                </span>
              </div>
            </van-cell>
          </van-cell-group>

          <van-empty
            v-if="prompts.length === 0 && !loading"
            description="暂无提示词"
          />
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import api from '@/utils/api'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const searchQuery = ref('')
const prompts = ref<any[]>([])
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = 20

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const loadPrompts = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    finished.value = false
    prompts.value = []
  }

  if (loading.value || finished.value) return

  loading.value = true

  try {
    const params = {
      skip: (page.value - 1) * pageSize,
      limit: pageSize,
      q: searchQuery.value || undefined
    }

    // 根据用户角色选择不同的API端点
    const endpoint = authStore.isAdmin ? '/api/v1/prompts/' : '/api/v1/prompts/public/'
    const response = await api.get(endpoint, { params })
    const newPrompts = response.data

    if (isRefresh) {
      prompts.value = newPrompts
    } else {
      prompts.value.push(...newPrompts)
    }

    if (newPrompts.length < pageSize) {
      finished.value = true
    } else {
      page.value++
    }
  } catch (error) {
    console.error('Failed to load prompts:', error)
    showToast('加载提示词失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onRefresh = () => {
  refreshing.value = true
  loadPrompts(true)
}

const onLoad = () => {
  loadPrompts()
}

const copyPrompt = async (prompt: any) => {
  try {
    await navigator.clipboard.writeText(prompt.content)
    showToast('已复制到剪贴板')
  } catch (error) {
    console.error('Failed to copy:', error)
    showToast('复制失败')
  }
}

const editPrompt = (prompt: any) => {
  router.push(`/prompts/${prompt.id}/edit`)
}

const confirmDelete = async (prompt: any) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除提示词"${prompt.title}"吗？`,
      confirmButtonText: '删除',
      confirmButtonColor: '#ee0a24'
    })

    await deletePrompt(prompt)
  } catch (error) {
    // 用户取消删除
  }
}

const deletePrompt = async (prompt: any) => {
  try {
    await api.delete(`/api/v1/prompts/${prompt.id}`)
    showToast('删除成功')

    // 从列表中移除
    const index = prompts.value.findIndex(p => p.id === prompt.id)
    if (index > -1) {
      prompts.value.splice(index, 1)
    }
  } catch (error) {
    console.error('Failed to delete prompt:', error)
    showToast('删除失败')
  }
}

onMounted(() => {
  loadPrompts(true)
})
</script>

<style scoped>
.prompt-list {
  padding: 0;
}

.search-section {
  margin-bottom: 20px;
}

.prompt-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.prompt-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #969799;
  padding: 8px 0;
}

.create-info,
.update-info {
  color: #969799;
}

.update-info {
  color: #1989fa;
}
</style>
