<template>
  <div class="notes-download-container">
    <div class="page-header">
      <van-button
        icon="arrow-left"
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">笔记下载</h1>
      <div class="header-spacer"></div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <van-cell-group inset>
        <van-cell title="统计信息" />
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats.total_notes }}</div>
            <div class="stat-label">总笔记</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.unused_notes }}</div>
            <div class="stat-label">未使用</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.used_notes }}</div>
            <div class="stat-label">已使用</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.usage_rate }}%</div>
            <div class="stat-label">使用率</div>
          </div>
        </div>
      </van-cell-group>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <van-search
        v-model="searchQuery"
        placeholder="搜索笔记标题、内容或标签"
        @search="handleSearch"
        @clear="handleSearch"
      />

      <!-- 商品搜索框 -->
      <van-search
        v-model="productSearchQuery"
        placeholder="搜索商品名称"
        @search="handleProductSearch"
        @input="handleProductSearchInput"
        @clear="handleProductSearchClear"
      />

      <!-- 商品筛选 -->
      <div class="product-filter">
        <van-field
          v-model="selectedProductText"
          label="商品筛选"
          placeholder="点击选择商品"
          readonly
          is-link
          @click="showProductPicker = true"
        />
        <van-button
          v-if="selectedProductId"
          size="mini"
          type="default"
          @click="clearProductFilter"
          class="clear-btn"
        >
          清除
        </van-button>
      </div>

      <div class="filter-tabs">
        <van-tabs v-model:active="activeTab" @change="handleTabChange">
          <van-tab title="全部" name="all" />
          <van-tab title="未使用" name="UNUSED" />
          <van-tab title="已使用" name="USED" />
        </van-tabs>
      </div>


    </div>

    <!-- 商品选择器 -->
    <van-popup v-model:show="showProductPicker" position="bottom" :style="{ height: '70%' }">
      <div class="product-picker">
        <div class="picker-header">
          <h3>选择商品</h3>
          <van-button size="mini" @click="showProductPicker = false">关闭</van-button>
        </div>

        <van-search
          v-model="productSearchQuery"
          placeholder="搜索商品名称"
          @search="handleProductSearch"
          @clear="handleProductSearch"
          @input="handleProductSearchInput"
        />

        <van-list
          v-model:loading="productLoading"
          :finished="productFinished"
          finished-text="没有更多了"
          @load="loadProducts"
        >
          <!-- 全部商品选项 -->
          <van-cell
            title="全部商品"
            label="显示所有商品的笔记"
            is-link
            @click="selectProduct(null)"
          />
          
          <van-cell
            v-for="product in products"
            :key="product.id"
            :title="product.title || product.short_name"
            :label="`笔记数量: ${product.notes_count || 0}`"
            is-link
            @click="selectProduct(product)"
          />
        </van-list>
      </div>
    </van-popup>

    <!-- 笔记列表 -->
    <div class="notes-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div class="notes-grid">
            <NotePreviewCard
              v-for="note in notes.filter(n => n && n.id)"
              :key="note.id"
              :note="note"
              @update-usage-status="handleUpdateUsageStatus"
            />
          </div>

          <van-empty
            v-if="!loading && notes.length === 0"
            description="暂无笔记"
            image="search"
          />
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import api from '@/utils/api'
import NotePreviewCard from '@/components/NotePreviewCard.vue'

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: number | null = null
  return (...args: any[]) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const productSearchQuery = ref('')
const activeTab = ref('all') // 默认显示所有笔记

// 类型定义
interface NoteItem {
  id: number
  title: string
  body?: string
  tags?: string
  cover_images_candidate?: string[]
  selected_cover_image?: string
  note_images?: string[]
  videos?: string[]
  usage_status: 'UNUSED' | 'USED'
  status: string
  created_at: string
  updated_at: string
  product_id: number
  product_title?: string
  product_short_name?: string
  product_name?: string
  scheduled_at?: string
}

interface ProductItem {
  id: number
  title?: string
  short_name?: string
  description?: string
  notes_count?: number
}

interface StatsData {
  total_notes: number
  unused_notes: number
  used_notes: number
  usage_rate: number
}

const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const notes = ref<NoteItem[]>([])
const stats = ref<StatsData>({
  total_notes: 0,
  unused_notes: 0,
  used_notes: 0,
  usage_rate: 0
})

// 商品筛选相关
const selectedProductId = ref<number | null>(null)
const selectedProductText = ref('')
const showProductPicker = ref(false)
const products = ref<ProductItem[]>([])
const productLoading = ref(false)
const productFinished = ref(false)
const productPage = ref(1)
const productPageSize = ref(20)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(20)
const isLoading = ref(false) // 添加加载状态，防止重复请求

// 验证笔记数据有效性
const validateNote = (note: unknown): note is NoteItem => {
  return typeof note === 'object' && 
         note !== null && 
         'id' in note && 
         typeof (note as NoteItem).id === 'number' && 
         (note as NoteItem).id > 0
}

// 加载笔记列表
const loadNotes = async (reset = false) => {
  // 防止重复请求
  if (isLoading.value) {
    console.log('Already loading, skipping request')
    return
  }

  if (reset) {
    currentPage.value = 1
    finished.value = false
    notes.value = []
  }

  isLoading.value = true

  try {
    const params: Record<string, string | number> = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }

    if (searchQuery.value.trim()) {
      params.q = searchQuery.value.trim()
    }

    if (productSearchQuery.value.trim()) {
      params.product_search = productSearchQuery.value.trim()
    }

    if (activeTab.value !== 'all') {
      params.usage_status = activeTab.value
    }

    if (selectedProductId.value) {
      params.product_id = selectedProductId.value
    }

    const response = await api.get('/api/v1/notes-download/preview', { params })
    const newNotes = response.data

    // 验证返回的数据
    const validNotes = newNotes.filter(validateNote)
    
    if (validNotes.length !== newNotes.length) {
      console.warn(`过滤了 ${newNotes.length - validNotes.length} 个无效笔记`)
    }

    if (reset) {
      // 重置时直接设置新数据
      notes.value = validNotes
    } else {
      // 追加时检查重复（安全措施，正常情况下不应该有重复）
      const existingIds = new Set(notes.value.map((note: NoteItem) => note.id))
      const uniqueNewNotes = validNotes.filter((note: NoteItem) => !existingIds.has(note.id))
      
      if (uniqueNewNotes.length !== validNotes.length) {
        console.warn(`检测到 ${validNotes.length - uniqueNewNotes.length} 个重复笔记，已过滤`)
      }
      
      notes.value = [...notes.value, ...uniqueNewNotes]
    }

    // 判断是否还有更多数据
    if (validNotes.length < pageSize.value) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('Failed to load notes:', error)
    showToast('加载笔记失败')
  } finally {
    isLoading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const params: Record<string, string | number> = {}

    // 只传递搜索相关的参数，统计数据应该显示全局信息
    // 不传递 usage_status 参数，因为后端明确声明统计应该显示全局数据
    if (searchQuery.value.trim()) {
      params.q = searchQuery.value.trim()
    }

    if (productSearchQuery.value.trim()) {
      params.product_search = productSearchQuery.value.trim()
    }

    if (selectedProductId.value) {
      params.product_id = selectedProductId.value
    }

    const response = await api.get('/api/v1/notes-download/stats', { params })
    stats.value = response.data
  } catch (error) {
    console.error('Failed to load stats:', error)
    // 设置默认统计值，避免显示错误的数据
    stats.value = {
      total_notes: 0,
      unused_notes: 0,
      used_notes: 0,
      usage_rate: 0
    }
  }
}

// 事件处理
const onLoad = () => {
  // 如果已经在加载中或已完成加载，直接返回
  if (isLoading.value || finished.value) {
    return
  }

  loading.value = true
  loadNotes().finally(() => {
    loading.value = false
  })
}

const onRefresh = () => {
  refreshing.value = true
  Promise.all([
    loadNotes(true),
    loadStats()
  ]).finally(() => {
    refreshing.value = false
  })
}

const handleSearch = () => {
  loadNotes(true)
  loadStats()
}

// 商品搜索处理
const handleProductSearch = () => {
  // 如果是在商品选择弹窗中，重置商品列表
  if (showProductPicker.value) {
    productPage.value = 1
    productFinished.value = false
    products.value = []
    loadProducts()
  } else {
    // 如果是在主页面，重新加载笔记和统计
    loadNotes(true)
    loadStats()
  }
}

const handleProductSearchInput = debounce(() => {
  if (showProductPicker.value) {
    // 在商品选择弹窗中的实时搜索
    productPage.value = 1
    productFinished.value = false
    products.value = []
    loadProducts()
  } else {
    // 在主页面的实时搜索
    loadNotes(true)
    loadStats()
  }
}, 500)

const handleProductSearchClear = () => {
  productSearchQuery.value = ''
  loadNotes(true)
  loadStats()
}

const handleTabChange = () => {
  loadNotes(true)
  loadStats()
}



const handleUpdateUsageStatus = async (noteId: number, status: 'UNUSED' | 'USED') => {
  try {
    showLoadingToast({
      message: '更新中...',
      forbidClick: true,
    })

    await api.put(`/api/v1/notes-download/${noteId}/usage-status`, {
      usage_status: status
    })

    closeToast()
    showToast(`已标记为${status === 'UNUSED' ? '未使用' : '已使用'}`)

    // 更新本地数据
    const noteIndex = notes.value.findIndex(note => note.id === noteId)
    if (noteIndex !== -1) {
      notes.value[noteIndex].usage_status = status
    }

    // 重新加载统计信息
    loadStats()
  } catch (error) {
    closeToast()
    console.error('Failed to update usage status:', error)
    showToast('更新失败')
  }
}

// 商品相关方法
const loadProducts = async () => {
  if (productLoading.value || productFinished.value) return

  productLoading.value = true

  try {
    const params = {
      skip: (productPage.value - 1) * productPageSize.value,
      limit: productPageSize.value,
      q: productSearchQuery.value || undefined
    }

    const response = await api.get('/api/v1/products/', { params })
    const newProducts = response.data

    if (productPage.value === 1) {
      products.value = newProducts
    } else {
      products.value.push(...newProducts)
    }

    if (newProducts.length < productPageSize.value) {
      productFinished.value = true
    } else {
      productPage.value++
    }
  } catch (error) {
    console.error('Failed to load products:', error)
    showToast('加载商品失败')
  } finally {
    productLoading.value = false
  }
}



const selectProduct = (product: ProductItem | null) => {
  if (product === null) {
    // 选择了"全部商品"
    selectedProductId.value = null
    selectedProductText.value = ''
  } else {
    selectedProductId.value = product.id
    selectedProductText.value = product.title || product.short_name || `商品${product.id}`
  }
  showProductPicker.value = false

  // 重新加载笔记
  loadNotes(true)
  loadStats()
}

const clearProductFilter = () => {
  selectedProductId.value = null
  selectedProductText.value = ''

  // 重新加载笔记
  loadNotes(true)
  loadStats()
}

onMounted(() => {
  loadStats()
  loadNotes(true)
})
</script>

<style scoped>
.notes-download-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.stats-section {
  padding: 16px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

.filter-section {
  background: white;
  margin-bottom: 8px;
}



.product-filter {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.product-filter .van-field {
  flex: 1;
}

.clear-btn {
  flex-shrink: 0;
}

.filter-tabs {
  padding: 0 16px;
}

.product-picker {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.picker-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.notes-list {
  padding: 0 16px;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notes-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .notes-list {
    padding: 0 12px;
  }
}

@media (min-width: 768px) and (max-width: 1200px) {
  .notes-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 12px;
  }
}

@media (min-width: 1200px) {
  .notes-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 16px;
  }

  .notes-list {
    padding: 0 24px;
  }
}
</style>
